import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
    }),
    tsconfigPaths({
      // Only use the project's tsconfig.json, not scanning parent directories
      projects: ["./tsconfig.json"],
    }),
  ],
  // Add this configuration to ignore .env file changes
  server: {
    watch: {
      ignored: ['**/.env']
    }
  }
});
