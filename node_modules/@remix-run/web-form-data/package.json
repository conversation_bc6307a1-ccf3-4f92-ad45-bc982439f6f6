{"name": "@remix-run/web-form-data", "version": "3.1.0", "description": "Web API compatible Form Data implementation", "files": ["src", "dist/src", "License.md", "Readme.md"], "keywords": ["formdata", "typed"], "type": "module", "module": "./src/lib.js", "main": "./dist/src/lib.node.cjs", "types": "./dist/src/lib.d.ts", "browser": {"./src/lib.node.js": "./src/lib.js"}, "exports": {".": {"types": "./dist/src/lib.d.ts", "browser": {"require": "./dist/src/lib.cjs", "import": "./src/lib.js"}, "require": "./dist/src/lib.node.cjs", "import": "./src/lib.node.js"}}, "dependencies": {"web-encoding": "1.1.5"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://gozala.io)", "repository": "https://github.com/remix-run/web-std-io", "license": "MIT", "devDependencies": {"@remix-run/web-blob": "^3.1.0", "@remix-run/web-fetch": "^4.4.0", "@remix-run/web-file": "^3.1.0", "@types/node": "15.0.2", "git-validate": "2.2.4", "husky": "^6.0.0", "lint-staged": "^11.0.0", "playwright-test": "^7.2.0", "prettier": "^2.3.0", "rimraf": "3.0.2", "rollup": "2.47.0", "rollup-plugin-multi-input": "1.2.0", "typescript": "^4.4.4", "uvu": "0.5.2"}, "scripts": {"typecheck": "tsc", "build": "npm run build:cjs && npm run build:types", "build:cjs": "rollup --config rollup.config.js", "build:types": "tsc --build", "prepare": "npm run build", "test:es": "uvu test all.spec.js", "test:web": "playwright-test -r uvu test/web.spec.js", "test:cjs": "rimraf dist && npm run build && node dist/test/all.spec.cjs", "test": "npm run test:es && npm run test:cjs", "precommit": "lint-staged"}, "lint-staged": {"*.js": ["prettier --no-semi --write", "git add"]}}