{"name": "micromark-util-character", "version": "1.2.0", "description": "micromark utility to handle character codes", "license": "MIT", "keywords": ["micromark", "util", "utility", "character"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-character", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "dev/index.d.ts", "files": ["dev/", "lib/", "index.d.ts", "index.js"], "exports": {"types": "./dev/index.d.ts", "development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"micromark-util-symbol": "^1.0.0", "micromark-util-types": "^1.0.0"}, "scripts": {"build": "micromark-build"}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}