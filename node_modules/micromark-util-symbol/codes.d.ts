export namespace codes {
  const carriageReturn: -5
  const lineFeed: -4
  const carriageReturnLineFeed: -3
  const horizontalTab: -2
  const virtualSpace: -1
  const eof: null
  const nul: 0
  const soh: 1
  const stx: 2
  const etx: 3
  const eot: 4
  const enq: 5
  const ack: 6
  const bel: 7
  const bs: 8
  const ht: 9
  const lf: 10
  const vt: 11
  const ff: 12
  const cr: 13
  const so: 14
  const si: 15
  const dle: 16
  const dc1: 17
  const dc2: 18
  const dc3: 19
  const dc4: 20
  const nak: 21
  const syn: 22
  const etb: 23
  const can: 24
  const em: 25
  const sub: 26
  const esc: 27
  const fs: 28
  const gs: 29
  const rs: 30
  const us: 31
  const space: 32
  const exclamationMark: 33
  const quotationMark: 34
  const numberSign: 35
  const dollarSign: 36
  const percentSign: 37
  const ampersand: 38
  const apostrophe: 39
  const leftParenthesis: 40
  const rightParenthesis: 41
  const asterisk: 42
  const plusSign: 43
  const comma: 44
  const dash: 45
  const dot: 46
  const slash: 47
  const digit0: 48
  const digit1: 49
  const digit2: 50
  const digit3: 51
  const digit4: 52
  const digit5: 53
  const digit6: 54
  const digit7: 55
  const digit8: 56
  const digit9: 57
  const colon: 58
  const semicolon: 59
  const lessThan: 60
  const equalsTo: 61
  const greaterThan: 62
  const questionMark: 63
  const atSign: 64
  const uppercaseA: 65
  const uppercaseB: 66
  const uppercaseC: 67
  const uppercaseD: 68
  const uppercaseE: 69
  const uppercaseF: 70
  const uppercaseG: 71
  const uppercaseH: 72
  const uppercaseI: 73
  const uppercaseJ: 74
  const uppercaseK: 75
  const uppercaseL: 76
  const uppercaseM: 77
  const uppercaseN: 78
  const uppercaseO: 79
  const uppercaseP: 80
  const uppercaseQ: 81
  const uppercaseR: 82
  const uppercaseS: 83
  const uppercaseT: 84
  const uppercaseU: 85
  const uppercaseV: 86
  const uppercaseW: 87
  const uppercaseX: 88
  const uppercaseY: 89
  const uppercaseZ: 90
  const leftSquareBracket: 91
  const backslash: 92
  const rightSquareBracket: 93
  const caret: 94
  const underscore: 95
  const graveAccent: 96
  const lowercaseA: 97
  const lowercaseB: 98
  const lowercaseC: 99
  const lowercaseD: 100
  const lowercaseE: 101
  const lowercaseF: 102
  const lowercaseG: 103
  const lowercaseH: 104
  const lowercaseI: 105
  const lowercaseJ: 106
  const lowercaseK: 107
  const lowercaseL: 108
  const lowercaseM: 109
  const lowercaseN: 110
  const lowercaseO: 111
  const lowercaseP: 112
  const lowercaseQ: 113
  const lowercaseR: 114
  const lowercaseS: 115
  const lowercaseT: 116
  const lowercaseU: 117
  const lowercaseV: 118
  const lowercaseW: 119
  const lowercaseX: 120
  const lowercaseY: 121
  const lowercaseZ: 122
  const leftCurlyBrace: 123
  const verticalBar: 124
  const rightCurlyBrace: 125
  const tilde: 126
  const del: 127
  const byteOrderMarker: 65279
  const replacementCharacter: 65533
}
