export {handlers as defaultHandlers} from './lib/handlers/index.js'
export {toEstree} from './lib/index.js'
export type ElementAttributeNameCase =
  import('./lib/state.js').ElementAttributeNameCase
export type Handle = import('./lib/state.js').Handle
export type Options = import('./lib/state.js').Options
export type Space = import('./lib/state.js').Space
export type State = import('./lib/state.js').State
export type StylePropertyNameCase =
  import('./lib/state.js').StylePropertyNameCase
