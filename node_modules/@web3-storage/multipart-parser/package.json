{"name": "@web3-storage/multipart-parser", "version": "1.0.0", "description": "A simple multipart/form-data parser to use with ReadableStreams", "main": "./cjs/src/index.js", "types": "./types/index.d.ts", "scripts": {"build": "npm run build:js && npm run build:types", "build:js": "ipjs build --main && npm run build:copy", "build:copy": "cp -a tsconfig.json src test dist/ ", "build:types": "npm run build:copy && cd dist && tsc --build", "test": "playwright-test -r tape -b webkit test/*.spec.js", "lint": "standard"}, "author": "vasco-santos", "license": "(Apache-2.0 AND MIT)", "repository": {"type": "git", "url": "git+https://github.com/web3-storage/multipart-parser.git"}, "standard": {"ignore": ["dist"]}, "devDependencies": {"ipjs": "^5.0.3", "playwright-test": "^6.0.0", "standard": "^16.0.3", "tape": "^5.2.2", "typescript": "^4.3.5", "web-streams-polyfill": "^3.1.0"}, "browser": {".": "./cjs/src/index.js"}, "exports": {".": {"browser": "./esm/src/index.js", "require": "./cjs/src/index.js", "import": "./esm/src/index.js"}}}