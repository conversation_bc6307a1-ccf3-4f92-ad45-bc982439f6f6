{
  "compilerOptions": {
    "allowJs": true,
    "checkJs": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": false,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictFunctionTypes": false,
    "strictNullChecks": true,
    "strictPropertyInitialization": true,
    "strictBindCallApply": true,
    "strict": true,
    "alwaysStrict": true,
    "esModuleInterop": true,
    "target": "ES2018",
    "module": "ESNext",
    "moduleResolution": "node",
    "declaration": true,
    "declarationMap": true,
    "outDir": "types",
    "skipLibCheck": true,
    "stripInternal": true,
    "resolveJsonModule": true,
    "emitDeclarationOnly": true,
    "baseUrl": ".",
  },
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules",
    "cjs",
    "esm",
    "test"
  ],
  "compileOnSave": false
}