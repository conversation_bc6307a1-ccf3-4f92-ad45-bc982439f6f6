import { createServerClient } from "@supabase/auth-helpers-remix";
import type { Database } from "~/types/database";
import { redirect } from "@remix-run/node";

export function createServerSupabase(request: Request, response: Response) {
  const supabaseUrl = process.env.SUPABASE_URL || "";
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || "";

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error("Missing Supabase URL or Anon Key in server environment");
    console.error("Please set SUPABASE_URL and SUPABASE_ANON_KEY in your .env file");
    throw new Error("Supabase configuration is missing. Please check your .env file.");
  }

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    request,
    response,
  });
}

export async function requireAuth(request: Request) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  const { data } = await supabase.auth.getSession();

  if (!data.session) {
    throw redirect("/login");
  }

  return {
    supabase,
    session: data.session,
  };
}

export async function getUserRole(request: Request) {
  const { supabase, session } = await requireAuth(request);
  
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", session.user.id)
    .single();

  return profile?.role || "client";
}
