import { createBrowserClient } from "@supabase/auth-helpers-remix";
import type { Database } from "~/types/database";

export function createClientSupabase() {
  // Only run on the client side
  if (typeof window === "undefined") {
    return null;
  }

  // Get environment variables from window.ENV (set in root.tsx)
  const supabaseUrl = (window as any).ENV?.SUPABASE_URL || "https://your-project-id.supabase.co";
  const supabaseAnonKey = (window as any).ENV?.SUPABASE_ANON_KEY || "your-anon-key-here";

  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
}
