import { ReactNode } from "react";

type GradientVariant = "primary" | "secondary" | "accent" | "dark";

interface BrandGradientProps {
  children: ReactNode;
  variant?: GradientVariant;
  className?: string;
}

export function BrandGradient({ 
  children, 
  variant = "primary", 
  className = "" 
}: BrandGradientProps) {
  const variantClasses = {
    primary: "bg-gradient-to-r from-accent-600 to-accent-400",
    secondary: "bg-gradient-to-r from-gray-800 to-gray-600",
    accent: "bg-gradient-to-r from-accent-500 via-accent-400 to-accent-300",
    dark: "bg-gradient-to-r from-gray-900 to-gray-700",
  };

  return (
    <div className={`${variantClasses[variant]} ${className}`}>
      {children}
    </div>
  );
}

interface BrandDividerProps {
  className?: string;
}

export function BrandDivider({ className = "" }: BrandDividerProps) {
  return (
    <div className={`h-1 bg-gradient-to-r from-accent-600 to-accent-400 ${className}`}></div>
  );
}

// Add displayNames to fix React errors
BrandGradient.displayName = "BrandGradient";
BrandDivider.displayName = "BrandDivider";
