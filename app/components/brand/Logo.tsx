import React from "react";
import { Link } from "@remix-run/react";

interface LogoProps {
  variant?: "default" | "minimal" | "icon";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  animated?: boolean;
  linkTo?: string;
}

export function Logo({
  variant = "default",
  size = "md",
  className = "",
  animated = true,
  linkTo,
}: LogoProps) {
  const sizeClasses = {
    sm: variant === "icon" ? "h-8 w-8" : "h-8",
    md: variant === "icon" ? "h-10 w-10" : "h-10",
    lg: variant === "icon" ? "h-12 w-12" : "h-12",
    xl: variant === "icon" ? "h-16 w-16" : "h-16",
  };
  
  const combinedClasses = `${sizeClasses[size]} ${className}`;
  
  const renderLogo = () => {
    if (variant === "icon") {
      return (
        <div className={`relative ${combinedClasses}`}>
          <div className={`absolute inset-0 bg-accent-500 rounded-lg ${animated ? 'animate-pulse-soft' : ''}`} style={{ opacity: 0.2 }}></div>
          <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-full h-full">
            <rect width="40" height="40" rx="8" fill="#000000" />
            <path d="M20 8L32 28H8L20 8Z" fill="#FFF02B" />
          </svg>
        </div>
      );
    }
    
    return (
      <div className={`relative ${combinedClasses} flex items-center`}>
        <div className="relative mr-2">
          <div className={`absolute inset-0 bg-accent-500 rounded-lg ${animated ? 'animate-pulse-soft' : ''}`} style={{ opacity: 0.2 }}></div>
          <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-full w-auto">
            <rect width="40" height="40" rx="8" fill="#000000" />
            <path d="M20 8L32 28H8L20 8Z" fill="#FFF02B" />
          </svg>
        </div>
        
        {variant === "default" && (
          <div className="flex flex-col">
            <span className="text-text-primary font-bold tracking-tight leading-none">
              Sia Moon
            </span>
            <span className="text-text-tertiary text-xs tracking-wide leading-none">
              Property Care
            </span>
          </div>
        )}
      </div>
    );
  };
  
  if (linkTo) {
    return (
      <Link to={linkTo} className="inline-flex focus:outline-none">
        {renderLogo()}
      </Link>
    );
  }
  
  return renderLogo();
}
