import { ReactNode } from "react";
import { Container } from "~/components/ui/Container";
import { BrandGradient } from "./BrandGradient";

interface HeroSectionProps {
  title: ReactNode;
  subtitle?: string;
  description?: string;
  children?: ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
  align?: "center" | "left" | "right";
}

export function HeroSection({
  title,
  subtitle,
  description,
  children,
  size = "lg",
  align = "center",
}: HeroSectionProps) {
  const sizeClasses = {
    sm: "py-12 sm:py-16",
    md: "py-16 sm:py-20",
    lg: "py-20 sm:py-24",
    xl: "py-24 sm:py-32",
  };

  const alignClasses = {
    center: "text-center mx-auto",
    left: "text-left",
    right: "text-right ml-auto",
  };

  const maxWidthClasses = {
    center: "max-w-4xl mx-auto",
    left: "max-w-4xl",
    right: "max-w-4xl ml-auto",
  };

  return (
    <BrandGradient variant="hero">
      <Container className={`${sizeClasses[size]}`}>
        <div className={`${alignClasses[align]}`}>
          {subtitle && (
            <div className="text-accent-500 text-sm font-medium uppercase tracking-wider mb-3">
              {subtitle}
            </div>
          )}
          
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-text-primary mb-6 leading-tight tracking-tight">
            {title}
          </h1>
          
          {description && (
            <p className={`text-lg text-text-secondary leading-relaxed ${maxWidthClasses[align]} mb-8`}>
              {description}
            </p>
          )}
          
          {children}
        </div>
      </Container>
    </BrandGradient>
  );
}

// Add displayName to fix React error
HeroSection.displayName = "HeroSection";
