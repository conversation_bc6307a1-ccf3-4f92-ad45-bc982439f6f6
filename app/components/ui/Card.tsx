import { ReactNode } from "react";

type CardVariant = "flat" | "elevated" | "outlined";

interface CardProps {
  children: ReactNode;
  variant?: CardVariant;
  className?: string;
  hover?: boolean;
}

export function Card({ 
  children, 
  variant = "flat", 
  className = "",
  hover = false
}: CardProps) {
  const variantClasses = {
    flat: "bg-surface-secondary",
    elevated: "bg-surface-secondary shadow-sm",
    outlined: "border border-border-primary",
  };

  const hoverClass = hover ? "transition-all duration-200 hover:shadow-md hover:translate-y-[-2px]" : "";

  return (
    <div className={`rounded-lg overflow-hidden ${variantClasses[variant]} ${hoverClass} ${className}`}>
      {children}
    </div>
  );
}

interface CardHeaderProps {
  children: ReactNode;
  className?: string;
}

export function CardHeader({ children, className = "" }: CardHeaderProps) {
  return (
    <div className={`px-4 py-3 border-b border-border-primary ${className}`}>
      {children}
    </div>
  );
}

interface CardContentProps {
  children: ReactNode;
  className?: string;
}

export function CardContent({ children, className = "" }: CardContentProps) {
  return (
    <div className={`px-4 py-4 ${className}`}>
      {children}
    </div>
  );
}

interface CardFooterProps {
  children: ReactNode;
  className?: string;
}

export function CardFooter({ children, className = "" }: CardFooterProps) {
  return (
    <div className={`px-4 py-3 border-t border-border-primary ${className}`}>
      {children}
    </div>
  );
}

interface CardTitleProps {
  children: ReactNode;
  className?: string;
}

export function CardTitle({ children, className = "" }: CardTitleProps) {
  return (
    <h3 className={`text-lg font-semibold text-text-primary ${className}`}>
      {children}
    </h3>
  );
}

interface CardDescriptionProps {
  children: ReactNode;
  className?: string;
}

export function CardDescription({ children, className = "" }: CardDescriptionProps) {
  return (
    <p className={`text-sm text-text-tertiary ${className}`}>
      {children}
    </p>
  );
}

// Add displayNames to fix React errors
Card.displayName = "Card";
CardHeader.displayName = "CardHeader";
CardContent.displayName = "CardContent";
CardFooter.displayName = "CardFooter";
CardTitle.displayName = "CardTitle";
CardDescription.displayName = "CardDescription";
