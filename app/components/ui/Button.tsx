import { ReactNode, ButtonHTMLAttributes } from "react";
import { Link } from "@remix-run/react";

type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "danger";
type ButtonSize = "xs" | "sm" | "md" | "lg" | "xl";

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  className?: string;
}

export function Button({ 
  children, 
  variant = "primary", 
  size = "md", 
  fullWidth = false,
  className = "",
  ...props 
}: ButtonProps) {
  const variantClasses = {
    primary: "bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500/50",
    secondary: "bg-surface-tertiary text-text-primary hover:bg-surface-quaternary focus:ring-gray-500/50",
    outline: "border border-border-primary text-text-primary hover:bg-surface-tertiary focus:ring-gray-500/50",
    ghost: "text-text-primary hover:bg-surface-tertiary focus:ring-gray-500/50",
    danger: "bg-red-500 text-white hover:bg-red-600 focus:ring-red-500/50",
  };

  const sizeClasses = {
    xs: "text-xs px-2 py-1",
    sm: "text-sm px-2.5 py-1.5",
    md: "text-sm px-4 py-2",
    lg: "text-base px-6 py-3",
    xl: "text-lg px-8 py-4",
  };

  const widthClass = fullWidth ? "w-full" : "";

  return (
    <button
      className={`inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

interface ButtonLinkProps {
  children: ReactNode;
  to: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  className?: string;
  prefetch?: "intent" | "render" | "none";
  target?: string;
  rel?: string;
}

export function ButtonLink({
  children,
  to,
  variant = "primary",
  size = "md",
  fullWidth = false,
  className = "",
  prefetch = "none",
  target,
  rel,
  ...props
}: ButtonLinkProps) {
  const variantClasses = {
    primary: "bg-accent-500 text-white hover:bg-accent-600 focus:ring-accent-500/50",
    secondary: "bg-surface-tertiary text-text-primary hover:bg-surface-quaternary focus:ring-gray-500/50",
    outline: "border border-border-primary text-text-primary hover:bg-surface-tertiary focus:ring-gray-500/50",
    ghost: "text-text-primary hover:bg-surface-tertiary focus:ring-gray-500/50",
    danger: "bg-red-500 text-white hover:bg-red-600 focus:ring-red-500/50",
  };

  const sizeClasses = {
    xs: "text-xs px-2 py-1",
    sm: "text-sm px-2.5 py-1.5",
    md: "text-sm px-4 py-2",
    lg: "text-base px-6 py-3",
    xl: "text-lg px-8 py-4",
  };

  const widthClass = fullWidth ? "w-full" : "";

  return (
    <Link
      to={to}
      prefetch={prefetch}
      target={target}
      rel={rel}
      className={`inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`}
      {...props}
    >
      {children}
    </Link>
  );
}

// Add displayNames to fix React errors
Button.displayName = "Button";
ButtonLink.displayName = "ButtonLink";
