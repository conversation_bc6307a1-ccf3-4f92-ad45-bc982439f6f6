import { useState } from "react";

type AvatarSize = "xs" | "sm" | "md" | "lg" | "xl";

interface AvatarProps {
  src?: string;
  alt: string;
  size?: AvatarSize;
  className?: string;
  fallback?: string;
}

export function Avatar({ 
  src, 
  alt, 
  size = "md", 
  className = "",
  fallback
}: AvatarProps) {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    xs: "w-6 h-6 text-xs",
    sm: "w-8 h-8 text-sm",
    md: "w-10 h-10 text-base",
    lg: "w-12 h-12 text-lg",
    xl: "w-16 h-16 text-xl",
  };

  const handleImageError = () => {
    setImageError(true);
  };

  // Get initials from alt text for fallback
  const getInitials = () => {
    if (fallback) return fallback;
    
    return alt
      .split(" ")
      .map(word => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div 
      className={`relative inline-flex items-center justify-center rounded-full overflow-hidden bg-surface-tertiary ${sizeClasses[size]} ${className}`}
    >
      {src && !imageError ? (
        <img
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          onError={handleImageError}
        />
      ) : (
        <span className="font-medium text-text-secondary">
          {getInitials()}
        </span>
      )}
    </div>
  );
}

// Add displayName to fix React error
Avatar.displayName = "Avatar";
