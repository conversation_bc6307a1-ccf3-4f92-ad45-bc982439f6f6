@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors */
    --color-accent-50: 255 251 235;
    --color-accent-100: 254 243 199;
    --color-accent-200: 253 230 138;
    --color-accent-300: 252 211 77;
    --color-accent-400: 251 191 36;
    --color-accent-500: 245 158 11;
    --color-accent-600: 217 119 6;
    --color-accent-700: 180 83 9;
    --color-accent-800: 146 64 14;
    --color-accent-900: 120 53 15;
    --color-accent-950: 69 26 3;

    /* Light mode surface colors */
    --color-surface-primary: 255 255 255;
    --color-surface-secondary: 250 250 250;
    --color-surface-tertiary: 245 245 245;
    --color-surface-quaternary: 240 240 240;

    /* Light mode text colors */
    --color-text-primary: 23 23 23;
    --color-text-secondary: 64 64 64;
    --color-text-tertiary: 115 115 115;

    /* Light mode border colors */
    --color-border-primary: 229 229 229;
    --color-border-secondary: 212 212 212;
  }

  .dark {
    /* Dark mode colors */
    --color-accent-50: 69 26 3;
    --color-accent-100: 120 53 15;
    --color-accent-200: 146 64 14;
    --color-accent-300: 180 83 9;
    --color-accent-400: 217 119 6;
    --color-accent-500: 245 158 11;
    --color-accent-600: 251 191 36;
    --color-accent-700: 252 211 77;
    --color-accent-800: 253 230 138;
    --color-accent-900: 254 243 199;
    --color-accent-950: 255 251 235;

    /* Dark mode surface colors */
    --color-surface-primary: 23 23 23;
    --color-surface-secondary: 38 38 38;
    --color-surface-tertiary: 64 64 64;
    --color-surface-quaternary: 82 82 82;

    /* Dark mode text colors */
    --color-text-primary: 250 250 250;
    --color-text-secondary: 229 229 229;
    --color-text-tertiary: 163 163 163;

    /* Dark mode border colors */
    --color-border-primary: 64 64 64;
    --color-border-secondary: 82 82 82;
  }
}
