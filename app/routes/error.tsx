import { Link } from "@remix-run/react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container, Section } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { BrandGradient } from "~/components/brand/BrandGradient";

export default function ErrorPage() {
  return (
    <MobileLayout hideNavBar hideFooter>
      <BrandGradient variant="hero" className="min-h-screen flex items-center justify-center">
        <Container size="md">
          <div className="text-center">
            <div className="mb-6 inline-flex items-center justify-center w-24 h-24 rounded-full bg-surface-tertiary text-accent-500">
              <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-text-primary mb-4">Something went wrong</h1>
            <p className="text-text-secondary text-lg mb-8 max-w-md mx-auto">
              We apologize for the inconvenience. Please try again or contact our support team if the problem persists.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
              <Link to="/">
                <Button variant="outline" size="lg">
                  Return Home
                </Button>
              </Link>
            </div>
          </div>
        </Container>
      </BrandGradient>
    </MobileLayout>
  );
}
