import { Link } from "@remix-run/react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container, Section } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { BrandGradient } from "~/components/brand/BrandGradient";

export default function NotFoundPage() {
  return (
    <MobileLayout hideNavBar hideFooter>
      <BrandGradient variant="hero" className="min-h-screen flex items-center justify-center">
        <Container size="md">
          <div className="text-center">
            <div className="mb-6 inline-flex items-center justify-center w-24 h-24 rounded-full bg-surface-tertiary text-accent-500">
              <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-text-primary mb-4">Page Not Found</h1>
            <p className="text-text-secondary text-lg mb-8 max-w-md mx-auto">
              The page you are looking for doesn't exist or has been moved. Please check the URL or return to the home page.
            </p>
            <Link to="/">
              <Button variant="primary" size="lg">
                Return Home
              </Button>
            </Link>
          </div>
        </Container>
      </BrandGradient>
    </MobileLayout>
  );
}
