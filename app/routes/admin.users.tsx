import { json, redirect } from "@remix-run/node";
import { useLoader<PERSON><PERSON>, <PERSON> } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

export async function loader({ request }: LoaderFunctionArgs) {
  // Check for admin session cookie
  const cookieHeader = request.headers.get("Cookie");
  const hasAdminSession = cookieHeader?.includes("admin_session=true");
  
  if (!hasAdminSession) {
    return redirect("/developers");
  }
  
  // Mock user data for development
  const mockUsers = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "client",
      created_at: "2024-01-15",
      status: "active"
    },
    {
      id: "2", 
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "client",
      created_at: "2024-01-20",
      status: "active"
    },
    {
      id: "3",
      name: "System Administrator",
      email: "<EMAIL>",
      role: "admin",
      created_at: "2024-01-01",
      status: "active"
    }
  ];
  
  return json({
    user: {
      name: "System Administrator",
      email: "<EMAIL>"
    },
    users: mockUsers
  });
}

export default function AdminUsers() {
  const { user, users } = useLoaderData<typeof loader>();
  
  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
              <p className="text-gray-600">Manage system users and their permissions</p>
            </div>
            <Link to="/admin">
              <Button variant="outline" size="sm">
                ← Back to Dashboard
              </Button>
            </Link>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-blue-600">{users.length}</div>
              <div className="text-sm text-gray-600">Total Users</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-green-600">
                {users.filter(u => u.role === 'admin').length}
              </div>
              <div className="text-sm text-gray-600">Administrators</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-purple-600">
                {users.filter(u => u.role === 'client').length}
              </div>
              <div className="text-sm text-gray-600">Clients</div>
            </div>
          </div>
          
          {/* Users Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">All Users</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joined
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.role === 'admin' 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(user.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button variant="outline" size="xs" className="mr-2">
                          Edit
                        </Button>
                        {user.role !== 'admin' && (
                          <Button variant="danger" size="xs">
                            Suspend
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Development Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Development Mode</h3>
                <div className="mt-1 text-sm text-yellow-700">
                  <p>This is showing mock user data. In production, this would connect to your actual user database.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </MobileLayout>
  );
}
