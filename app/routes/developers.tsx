import { useState, useEffect } from "react";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData } from "@remix-run/react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";
import { createClientSupabase } from "~/utils/supabase.client";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

// Admin credentials - In production, these should be environment variables
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "SiaMoon2024!Admin";

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  // Check if user is already authenticated and is admin
  const { data: { session } } = await supabase.auth.getSession();
  
  if (session) {
    // Check if user has admin role
    const { data: profile } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", session.user.id)
      .single();
    
    if (profile?.role === "admin") {
      return redirect("/admin");
    }
  }
  
  return json({ 
    isAuthenticated: !!session,
    adminEmail: ADMIN_EMAIL 
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const action = formData.get("action") as string;
  
  if (action === "admin-login") {
    // Verify admin credentials
    if (email !== ADMIN_EMAIL || password !== ADMIN_PASSWORD) {
      return json({ error: "Invalid admin credentials" });
    }
    
    try {
      // Try to sign in with existing admin account
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      });
      
      if (signInError) {
        // If sign in fails, try to create the admin account
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: ADMIN_EMAIL,
          password: ADMIN_PASSWORD,
          options: {
            data: {
              role: "admin",
              name: "System Administrator"
            }
          }
        });
        
        if (signUpError) {
          return json({ error: `Failed to create admin account: ${signUpError.message}` });
        }
        
        // Create admin profile
        if (signUpData.user) {
          const { error: profileError } = await supabase
            .from("profiles")
            .upsert({
              id: signUpData.user.id,
              email: ADMIN_EMAIL,
              name: "System Administrator",
              role: "admin",
              phone: "******-000-0000",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          
          if (profileError) {
            console.error("Error creating admin profile:", profileError);
          }
        }
        
        // Try to sign in again after account creation
        const { error: retrySignInError } = await supabase.auth.signInWithPassword({
          email: ADMIN_EMAIL,
          password: ADMIN_PASSWORD,
        });
        
        if (retrySignInError) {
          return json({ error: `Admin account created but sign in failed: ${retrySignInError.message}` });
        }
      }
      
      return redirect("/admin", {
        headers: response.headers,
      });
      
    } catch (error) {
      console.error("Admin login error:", error);
      return json({ error: "An unexpected error occurred during admin login" });
    }
  }
  
  return json({ error: "Invalid action" });
}

export default function Developers() {
  const [supabase, setSupabase] = useState<any>(null);
  const [isClientReady, setIsClientReady] = useState(false);
  const actionData = useActionData<typeof action>();
  const loaderData = useLoaderData<typeof loader>();
  
  useEffect(() => {
    const client = createClientSupabase();
    setSupabase(client);
    setIsClientReady(true);
  }, []);
  
  if (!isClientReady) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <Container className="py-12">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Developer Access</h1>
            <p className="text-lg text-gray-600">
              Administrative access for Sia Moon Property Care system
            </p>
          </div>
          
          {/* Admin Login Form */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Admin Login</h2>
            
            {/* Error Display */}
            {actionData?.error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800 text-sm">{actionData.error}</p>
              </div>
            )}
            
            {/* Admin Credentials Info */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Admin Credentials:</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Email:</strong> {loaderData.adminEmail}</p>
                <p><strong>Password:</strong> SiaMoon2024!Admin</p>
              </div>
              <p className="text-xs text-blue-600 mt-2">
                These credentials provide full administrative access to the system.
              </p>
            </div>
            
            <Form method="post" className="space-y-6">
              <input type="hidden" name="action" value="admin-login" />
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  defaultValue={loaderData.adminEmail}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter admin email"
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter admin password"
                />
              </div>
              
              <Button
                type="submit"
                variant="primary"
                size="lg"
                fullWidth
                className="text-lg font-medium"
              >
                Login as Administrator
              </Button>
            </Form>
            
            {/* System Information */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-3">System Access Levels:</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                  <span><strong>Admin:</strong> Full system access, user management, settings</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                  <span><strong>Staff:</strong> Booking management, customer service</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span><strong>Client:</strong> Book services, view bookings</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Back to Home */}
          <div className="text-center mt-8">
            <a
              href="/"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </Container>
    </div>
  );
}
