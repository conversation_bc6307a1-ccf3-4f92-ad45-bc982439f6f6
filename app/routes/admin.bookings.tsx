import { json, redirect } from "@remix-run/node";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

export async function loader({ request }: LoaderFunctionArgs) {
  // Check for admin session cookie
  const cookieHeader = request.headers.get("Cookie");
  const hasAdminSession = cookieHeader?.includes("admin_session=true");

  if (!hasAdminSession) {
    return redirect("/developers");
  }

  // Mock booking data for development
  const mockBookings = [
    {
      id: "1",
      service: "Pool Maintenance",
      client_name: "<PERSON>",
      client_email: "<EMAIL>",
      property_address: "123 Ocean View Drive",
      scheduled_date: "2024-01-25",
      status: "confirmed",
      total_amount: 150,
      created_at: "2024-01-20"
    },
    {
      id: "2",
      service: "Cleaning Services",
      client_name: "<PERSON>",
      client_email: "<EMAIL>",
      property_address: "456 Beach Front Villa",
      scheduled_date: "2024-01-28",
      status: "pending",
      total_amount: 200,
      created_at: "2024-01-22"
    },
    {
      id: "3",
      service: "General Maintenance",
      client_name: "Mike Johnson",
      client_email: "<EMAIL>",
      property_address: "789 Sunset Boulevard",
      scheduled_date: "2024-01-30",
      status: "completed",
      total_amount: 300,
      created_at: "2024-01-18"
    }
  ];

  return json({
    user: {
      name: "System Administrator",
      email: "<EMAIL>"
    },
    bookings: mockBookings
  });
}

export default function AdminBookings() {
  const { user, bookings } = useLoaderData<typeof loader>();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalRevenue = bookings.reduce((sum: number, booking: any) => sum + booking.total_amount, 0);

  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Booking Management</h1>
              <p className="text-gray-600">View and manage all service bookings</p>
            </div>
            <Link to="/admin">
              <Button variant="outline" size="sm">
                ← Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-blue-600">{bookings.length}</div>
              <div className="text-sm text-gray-600">Total Bookings</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-yellow-600">
                {bookings.filter((b: any) => b.status === 'pending').length}
              </div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-green-600">
                {bookings.filter((b: any) => b.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-purple-600">${totalRevenue}</div>
              <div className="text-sm text-gray-600">Total Revenue</div>
            </div>
          </div>

          {/* Bookings Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">All Bookings</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Booking Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Property
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bookings.map((booking: any) => (
                    <tr key={booking.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{booking.service}</div>
                          <div className="text-sm text-gray-500">
                            Scheduled: {new Date(booking.scheduled_date).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{booking.client_name}</div>
                          <div className="text-sm text-gray-500">{booking.client_email}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{booking.property_address}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${booking.total_amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button variant="outline" size="xs" className="mr-2">
                          View
                        </Button>
                        {booking.status === 'pending' && (
                          <Button variant="primary" size="xs">
                            Confirm
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Development Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Development Mode</h3>
                <div className="mt-1 text-sm text-yellow-700">
                  <p>This is showing mock booking data. In production, this would connect to your actual booking database.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </MobileLayout>
  );
}