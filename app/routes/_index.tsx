import type { MetaFunction } from "@remix-run/node";
import { Container } from "~/components/ui/Container";
import { ButtonLink } from "~/components/ui/Button";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { ThemeToggle } from "~/components/ui/ThemeToggle";

export const meta: MetaFunction = () => {
  return [
    { title: "Sia Moon Property Care - Reliable Property Services for Island Living" },
    { name: "description", content: "Professional property care services including cleaning, pool maintenance, repairs, and emergency check-ins for villa owners across the island." },
  ];
};

// Services data with dark masculine styling
const services = [
  {
    id: "cleaning",
    name: "Cleaning Services",
    description: "Regular and deep cleaning services for your property, ensuring it remains pristine for you and your guests.",
    gradient: "from-accent-500 to-accent-600",
    icon: (
      <div className="p-4 rounded-xl bg-accent-500 text-black shadow-card border border-accent-600">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
        </svg>
      </div>
    ),
  },
  {
    id: "pool",
    name: "Pool Maintenance",
    description: "Complete pool care including cleaning, chemical balancing, and equipment checks to keep your pool sparkling and safe.",
    gradient: "from-secondary-600 to-secondary-800",
    icon: (
      <div className="p-4 rounded-xl bg-secondary-800 text-accent-500 shadow-card border border-secondary-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
        </svg>
      </div>
    ),
  },
  {
    id: "maintenance",
    name: "General Maintenance",
    description: "Preventative maintenance and upkeep to protect your investment and prevent small issues from becoming major problems.",
    gradient: "from-accent-600 to-accent-800",
    icon: (
      <div className="p-4 rounded-xl bg-accent-500 text-black shadow-card border border-accent-600">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
        </svg>
      </div>
    ),
  },
  {
    id: "emergency",
    name: "Emergency Check-ins",
    description: "Rapid response to emergencies, weather events, or unexpected issues when you're away from your property.",
    gradient: "from-accent-500 to-accent-700",
    icon: (
      <div className="p-4 rounded-xl bg-accent-500 text-black shadow-card border border-accent-600">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
      </div>
    ),
  },
  {
    id: "repairs",
    name: "Repairs & Renovations",
    description: "Professional repair services for everything from minor fixes to major renovations, with quality workmanship guaranteed.",
    gradient: "from-secondary-700 to-secondary-900",
    icon: (
      <div className="p-4 rounded-xl bg-secondary-800 text-accent-500 shadow-card border border-secondary-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21.75 6.75a4.5 4.5 0 01-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 11-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 016.336-4.486l-3.276 3.276a3.004 3.004 0 002.25 2.25l3.276-3.276c.256.565.398 1.192.398 1.852z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.867 19.125h.008v.008h-.008v-.008z" />
        </svg>
      </div>
    ),
  },
];

export default function Index() {
  // Replace with your actual WhatsApp number
  const whatsappNumber = "1234567890";
  const whatsappUrl = `https://wa.me/${whatsappNumber}`;

  return (
    <MobileLayout>
      {/* Hero Section - x.ai inspired clean design */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-surface-primary">
        {/* Minimal background pattern inspired by x.ai */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-gradient-mesh opacity-20"></div>
        </div>

        {/* Theme toggle in top right */}
        <div className="absolute top-6 right-6 z-10">
          <ThemeToggle />
        </div>

        <Container className="relative z-10">
          <div className="max-w-5xl mx-auto text-center">
            <div className="animate-fade-in-professional">
              {/* Company Name - Clean and Professional like x.ai */}
              <div className="mb-12 sm:mb-16">
                <h1 className="text-5xl sm:text-7xl lg:text-8xl font-bold text-text-primary mb-6 leading-none tracking-tight">
                  <span className="text-gradient">SIA MOON</span>
                </h1>
                <h2 className="text-2xl sm:text-4xl lg:text-5xl font-medium text-text-secondary tracking-wide">
                  Property Care
                </h2>
              </div>

              {/* Value Proposition - Clear and Direct like x.ai */}
              <div className="mb-12 sm:mb-16 max-w-4xl mx-auto">
                <p className="text-xl sm:text-2xl lg:text-3xl text-text-primary mb-6 font-medium leading-relaxed">
                  Professional property management for modern island living
                </p>
                <p className="text-lg sm:text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto">
                  Comprehensive care services designed for villa owners who demand excellence,
                  reliability, and peace of mind.
                </p>
              </div>

              {/* Primary CTA - Clean and focused */}
              <div className="mb-16 sm:mb-20">
                <ButtonLink
                  to="/book"
                  variant="primary"
                  size="xl"
                  className="text-lg font-medium px-10 py-4 min-w-[280px]"
                >
                  Book Service
                </ButtonLink>
              </div>

              {/* Key Features - Clean Grid inspired by x.ai */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-12 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-accent-500 text-sm font-medium uppercase tracking-wider mb-2">
                    Available 24/7
                  </div>
                  <div className="text-text-secondary text-sm leading-relaxed">
                    Emergency response and regular maintenance
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-accent-500 text-sm font-medium uppercase tracking-wider mb-2">
                    Licensed & Insured
                  </div>
                  <div className="text-text-secondary text-sm leading-relaxed">
                    Professional certification and full coverage
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-accent-500 text-sm font-medium uppercase tracking-wider mb-2">
                    5+ Years Experience
                  </div>
                  <div className="text-text-secondary text-sm leading-relaxed">
                    Proven track record in island property care
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Container>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg className="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* Services Section - x.ai inspired clean design */}
      <section className="py-20 sm:py-24 md:py-28 relative bg-surface-primary">
        <Container>
          <div className="text-center mb-16 sm:mb-20">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-text-primary mb-6 tracking-tight">
              Our <span className="text-gradient">Services</span>
            </h2>
            <p className="text-text-secondary text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
              Comprehensive property care solutions designed for modern villa owners who value quality and reliability.
            </p>
          </div>

          {/* Clean service grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-10">
            {services.map((service, index) => (
              <div
                key={service.id}
                className="group animate-fade-in-professional touch-manipulation"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-surface-secondary border border-border-primary rounded-xl p-8 h-full transition-all duration-200 hover:bg-surface-tertiary hover:border-border-secondary hover:shadow-elevated">
                  <div className="flex flex-col items-center text-center h-full">
                    <div className="mb-6 p-4 bg-surface-tertiary rounded-xl border border-border-primary group-hover:border-accent-500/20 transition-colors duration-200">
                      <div className="w-8 h-8 text-accent-500">
                        {service.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-4 group-hover:text-accent-500 transition-colors duration-200">
                      {service.name}
                    </h3>
                    <p className="text-text-secondary leading-relaxed flex-1">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section - x.ai inspired clean design */}
      <section className="py-20 sm:py-24 md:py-28 relative bg-surface-primary">
        <Container>
          <div className="bg-surface-secondary border border-border-primary rounded-xl p-12 sm:p-16 text-center">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-text-primary mb-6 tracking-tight">
                Ready to get started?
              </h2>
              <p className="text-lg sm:text-xl text-text-secondary mb-10 leading-relaxed">
                Experience professional property care that exceeds expectations.
                Book your service today and discover the difference quality makes.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <ButtonLink
                  to="/book"
                  variant="primary"
                  size="xl"
                  className="text-lg font-medium px-8 py-4 min-w-[200px]"
                >
                  Book Service
                </ButtonLink>

                <ButtonLink
                  to={whatsappUrl}
                  variant="secondary"
                  size="xl"
                  className="text-lg font-medium px-8 py-4 min-w-[200px]"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Contact Us
                </ButtonLink>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Developer Access - Subtle footer link */}
      <footer className="py-8 bg-surface-secondary border-t border-border-primary">
        <Container>
          <div className="text-center">
            <a
              href="/developers"
              className="text-xs text-text-tertiary hover:text-text-secondary transition-colors duration-200"
            >
              Developer Access
            </a>
          </div>
        </Container>
      </footer>
    </MobileLayout>
  );
}
