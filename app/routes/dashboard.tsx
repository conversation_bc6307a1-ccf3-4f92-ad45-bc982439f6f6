import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container, Section, Grid } from "~/components/ui/Container";
import { Card, CardContent, CardTitle, CardDescription, CardHeader, CardFooter } from "~/components/ui/Card";
import { Button, ButtonLink } from "~/components/ui/Button";
import { Badge } from "~/components/ui/Badge";
import { BrandDivider, BrandGradient } from "~/components/brand/BrandGradient";
import { Avatar } from "~/components/ui/Avatar";

export const loader = async () => {
  // Mock data for dashboard
  return json({
    user: {
      id: "1",
      email: "<EMAIL>",
      name: "<PERSON>",
      role: "client",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    },
    properties: [
      {
        id: "1",
        name: "Main Residence",
        address: "123 Main St, Anytown, CA 90210",
        type: "Residential",
        status: "Active",
        nextService: "2023-06-15",
        serviceType: "Cleaning",
      },
      {
        id: "2",
        name: "Beach House",
        address: "456 Ocean Dr, Beachtown, FL 33139",
        type: "Vacation",
        status: "Active",
        nextService: "2023-06-20",
        serviceType: "Landscaping",
      },
    ],
    upcomingServices: [
      {
        id: "1",
        propertyName: "Main Residence",
        serviceType: "Cleaning",
        date: "2023-06-15",
        time: "Morning (8AM - 12PM)",
        status: "Confirmed",
      },
      {
        id: "2",
        propertyName: "Beach House",
        serviceType: "Landscaping",
        date: "2023-06-20",
        time: "Afternoon (12PM - 4PM)",
        status: "Pending",
      },
    ],
    recentInvoices: [
      {
        id: "INV-001",
        date: "2023-05-15",
        amount: "$150.00",
        status: "Paid",
        description: "Monthly Cleaning Service",
      },
      {
        id: "INV-002",
        date: "2023-05-10",
        amount: "$200.00",
        status: "Paid",
        description: "Landscaping Service",
      },
      {
        id: "INV-003",
        date: "2023-05-01",
        amount: "$75.00",
        status: "Pending",
        description: "Maintenance Service",
      },
    ],
  });
};

export default function Dashboard() {
  const { user, properties, upcomingServices, recentInvoices } = useLoaderData<typeof loader>();

  return (
    <MobileLayout user={user}>
      {/* Dashboard Header */}
      <BrandGradient variant="primary" className="py-8">
        <Container>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <Avatar src={user.avatar} alt={user.name} size="lg" />
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-text-primary">Welcome back, {user.name}</h1>
                <p className="text-text-tertiary">Your dashboard overview</p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <ButtonLink to="/book" variant="primary" size="md">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Book Service
              </ButtonLink>
              <ButtonLink to="/properties/add" variant="outline" size="md">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3" />
                </svg>
                Add Property
              </ButtonLink>
            </div>
          </div>
        </Container>
      </BrandGradient>

      {/* Dashboard Content */}
      <Section className="bg-surface-primary pt-6">
        <Container>
          {/* Stats Overview */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <Card variant="elevated" className="text-center">
              <CardContent>
                <div className="text-3xl font-bold text-accent-500 mb-1">{properties.length}</div>
                <div className="text-text-tertiary text-sm">Properties</div>
              </CardContent>
            </Card>
            <Card variant="elevated" className="text-center">
              <CardContent>
                <div className="text-3xl font-bold text-accent-500 mb-1">{upcomingServices.length}</div>
                <div className="text-text-tertiary text-sm">Upcoming Services</div>
              </CardContent>
            </Card>
            <Card variant="elevated" className="text-center">
              <CardContent>
                <div className="text-3xl font-bold text-accent-500 mb-1">12</div>
                <div className="text-text-tertiary text-sm">Total Services</div>
              </CardContent>
            </Card>
            <Card variant="elevated" className="text-center">
              <CardContent>
                <div className="text-3xl font-bold text-accent-500 mb-1">3</div>
                <div className="text-text-tertiary text-sm">Invoices</div>
              </CardContent>
            </Card>
          </div>

          {/* Properties Section */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-text-primary">Your Properties</h2>
              <ButtonLink to="/properties" variant="ghost" size="sm">
                View All
                <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </ButtonLink>
            </div>
            
            <Grid cols={2} gap="md">
              {properties.map((property) => (
                <Card key={property.id} variant="elevated" hover className="h-full">
                  <CardContent>
                    <div className="flex justify-between items-start mb-2">
                      <CardTitle>{property.name}</CardTitle>
                      <Badge 
                        variant={property.status === "Active" ? "success" : "secondary"}
                        size="sm"
                      >
                        {property.status}
                      </Badge>
                    </div>
                    <CardDescription className="mb-3">{property.address}</CardDescription>
                    
                    <div className="flex flex-col space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-text-tertiary">Type:</span>
                        <span className="text-text-secondary font-medium">{property.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text-tertiary">Next Service:</span>
                        <span className="text-text-secondary font-medium">{new Date(property.nextService).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-text-tertiary">Service Type:</span>
                        <span className="text-text-secondary font-medium">{property.serviceType}</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-border-primary">
                      <ButtonLink to={`/properties/${property.id}`} variant="outline" size="sm" fullWidth>
                        Manage Property
                      </ButtonLink>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </Grid>
          </div>

          {/* Upcoming Services Section */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-text-primary">Upcoming Services</h2>
              <ButtonLink to="/services/schedule" variant="ghost" size="sm">
                View Schedule
                <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </ButtonLink>
            </div>
            
            <Card variant="elevated">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border-primary">
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Property</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Service</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Date</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Time</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Status</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-text-tertiary uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {upcomingServices.map((service, index) => (
                      <tr 
                        key={service.id} 
                        className={`${index !== upcomingServices.length - 1 ? 'border-b border-border-primary' : ''} hover:bg-surface-tertiary transition-colors duration-150`}
                      >
                        <td className="px-4 py-4 text-sm text-text-primary">{service.propertyName}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{service.serviceType}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{new Date(service.date).toLocaleDateString()}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{service.time}</td>
                        <td className="px-4 py-4 text-sm">
                          <Badge 
                            variant={service.status === "Confirmed" ? "success" : "warning"}
                            size="sm"
                          >
                            {service.status}
                          </Badge>
                        </td>
                        <td className="px-4 py-4 text-sm text-right">
                          <Button variant="ghost" size="xs">
                            Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>

          {/* Recent Invoices Section */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-text-primary">Recent Invoices</h2>
              <ButtonLink to="/invoices" variant="ghost" size="sm">
                View All
                <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </ButtonLink>
            </div>
            
            <Card variant="elevated">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border-primary">
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Invoice</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Date</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Description</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Amount</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-text-tertiary uppercase tracking-wider">Status</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-text-tertiary uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentInvoices.map((invoice, index) => (
                      <tr 
                        key={invoice.id} 
                        className={`${index !== recentInvoices.length - 1 ? 'border-b border-border-primary' : ''} hover:bg-surface-tertiary transition-colors duration-150`}
                      >
                        <td className="px-4 py-4 text-sm font-medium text-accent-500">{invoice.id}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{new Date(invoice.date).toLocaleDateString()}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{invoice.description}</td>
                        <td className="px-4 py-4 text-sm text-text-primary">{invoice.amount}</td>
                        <td className="px-4 py-4 text-sm">
                          <Badge 
                            variant={invoice.status === "Paid" ? "success" : "warning"}
                            size="sm"
                          >
                            {invoice.status}
                          </Badge>
                        </td>
                        <td className="px-4 py-4 text-sm text-right">
                          <Button variant="ghost" size="xs">
                            View
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        </Container>
      </Section>
    </MobileLayout>
  );
}
